import { Server } from "socket.io";
import http from "http";

const httpServer = http.createServer();
const port = Number(process.env.MESSAGE_SOCKET_PORT) || 3001;

console.log(Number(process.env.MESSAGE_SOCKET_PORT));

const io = new Server(httpServer, {
  cors: {
    origin: [process.env.NEXT_PUBLIC_HOST_URL as string],
    methods: ["GET", "POST"],
    credentials: true,
  },
});

io.on("connection", (socket) => {
  console.log("New client:", socket.id);
});

httpServer.listen(port, () => {
  console.log(`Socket.IO running on :${port}`);
});
